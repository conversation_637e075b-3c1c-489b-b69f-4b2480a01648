# 预设功能使用说明

## 功能概述

预设功能是AIChatBox中新增的全局AI行为控制功能，允许用户设置在所有AI交互场景中都生效的预设内容。

## 功能特点

### 1. 全局生效
预设内容会在以下所有AI交互场景中生效：
- 私聊对话
- 群聊对话  
- 朋友圈自动生成
- 日记自动生成
- 记忆提取
- 其他所有调用AI的功能

### 2. 位置控制
用户可以选择预设内容的注入位置：
- **前置注入**：预设内容会添加到系统提示词的最前面
- **后置注入**：预设内容会添加到系统提示词的最后面

### 3. 配置级别
预设功能是基于API配置的，每个API配置都可以有独立的预设设置。

## 使用方法

### 1. 设置预设
1. 进入"项圈"（API设置）页面
2. 在"预设"部分填写预设内容
3. 选择注入位置（前/后）
4. 点击"保存并设为当前配置"

### 2. 预设内容示例
```
你是一个专业的AI助手，请始终保持礼貌和专业的态度。
在回复时请注意以下几点：
1. 使用简洁明了的语言
2. 提供准确的信息
3. 保持友好的语气
```

### 3. 注入位置说明
- **选择"前"**：预设内容会在角色人设之前注入，具有更高的优先级
- **选择"后"**：预设内容会在角色人设之后注入，作为补充说明

## 技术实现

### 代码位置
- 界面：`src/js/js.js` 第183-197行（API设置页面）
- 数据保存：`src/js/js.js` 第6696-6704行（API配置保存）
- 注入逻辑：`src/js/js.js` 第3514-3523行（getAiReply函数）

### 数据结构
```javascript
// API配置中的预设字段
{
    presetContent: "预设内容文本",
    presetPosition: "before" | "after"
}
```

### 注入逻辑
```javascript
if (activeProfile && activeProfile.presetContent && activeProfile.presetContent.trim()) {
    const presetContent = activeProfile.presetContent.trim();
    const presetPosition = activeProfile.presetPosition || 'before';
    
    if (presetPosition === 'before') {
        systemPrompt = presetContent + '\n\n' + systemPrompt;
    } else {
        systemPrompt = systemPrompt + '\n\n' + presetContent;
    }
}
```

## 注意事项

1. **全局影响**：预设内容会影响所有AI交互，请谨慎设置
2. **配置绑定**：预设是绑定到特定API配置的，切换API配置会切换预设
3. **优先级**：前置注入的预设具有更高的优先级，可能会覆盖角色的部分行为
4. **兼容性**：现有的API配置会自动添加空的预设字段，不影响现有功能

## 使用建议

1. **通用规则**：可以设置一些通用的行为规则或约束
2. **风格控制**：统一AI的回复风格或语气
3. **安全限制**：添加内容安全相关的限制
4. **个性化**：根据个人喜好定制AI的行为模式

## 更新日志

- **v1.0**：初始版本，支持基本的预设内容和位置控制
